<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Superadmins $superadmins
 * @property Workunits $workunits
 * @property MsProvinces $msprovinces
 * @property MsCities $mscities
 * @property MsDistricts $msdistricts
 * @property MsVillages $msvillages
 * @property CI_Form_validation $form_validation
 */
class Register extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Superadmins', 'superadmins');
        $this->load->model('Workunits', 'workunits');
        $this->load->model('MsProvinces', 'msprovinces');
        $this->load->model('MsCities', 'mscities');
        $this->load->model('MsDistricts', 'msdistricts');
        $this->load->model('MsVillages', 'msvillages');
        $this->load->helper('security');
    }

    public function index()
    {
        // Redirect if already logged in
        if (isLogin()) {
            return redirect(base_url());
        }

        $data = array();
        $data['title'] = 'Registrasi BUMDes';
        $data['content'] = 'register/index';

        // Get list of work units for selection
        $data['workunits'] = $this->workunits->select('id, workunitname')->result();

        // Get list of provinces for selection
        $data['provinces'] = $this->msprovinces->order_by('name', 'ASC')->result();

        return $this->load->view('auth_master', $data);
    }

    public function process()
    {
        // Redirect if already logged in
        if (isLogin()) {
            return JSONResponseDefault('FAILED', 'Anda sudah login.');
        }

        $business_owner = getPost('business_owner');
        $business_name = getPost('business_name');
        $username = getPost('username');
        $password = getPost('password');
        $confirm_password = getPost('confirm_password');
        $workunit_ids = getPost('workunit_ids'); // Array of work unit IDs
        $province_id = getPost('province_id');
        $city_id = getPost('city_id');
        $district_id = getPost('district_id');
        $village_id = getPost('village_id');

        // Validasi input
        $this->form_validation->set_rules('business_owner', 'Nama Pemilik Usaha', 'required|trim');
        $this->form_validation->set_rules('business_name', 'Nama Usaha', 'required|trim');
        $this->form_validation->set_rules('username', 'Username', 'required|trim|min_length[4]|is_unique[msusers.username]');
        $this->form_validation->set_rules('password', 'Password', 'required|trim|min_length[6]');
        $this->form_validation->set_rules('confirm_password', 'Konfirmasi Password', 'required|trim|matches[password]');
        $this->form_validation->set_rules('province_id', 'Provinsi', 'required');
        $this->form_validation->set_rules('city_id', 'Kabupaten/Kota', 'required');
        $this->form_validation->set_rules('district_id', 'Kecamatan', 'required');
        $this->form_validation->set_rules('village_id', 'Desa', 'required');

        if ($this->form_validation->run() == false) {
            return JSONResponseDefault('FAILED', strip_tags(validation_errors()));
        }

        // Validate work units
        if (empty($workunit_ids) || !is_array($workunit_ids)) {
            return JSONResponseDefault('FAILED', 'Pilih minimal satu unit usaha.');
        }

        // Check if village exists
        $village = $this->db->get_where('msvillages', array('code' => $village_id))->row();
        if (!$village) {
            return JSONResponseDefault('FAILED', 'Desa tidak ditemukan.');
        }

        $this->db->trans_begin();

        try {
            // Insert into msusers table
            $user_data = array(
                'name' => $business_owner,
                'businessname' => $business_name,
                'username' => $username,
                'password' => password_hash($password, PASSWORD_DEFAULT),
                'role' => 'BUMDes',
                'villageid' => $village_id,
                'workunitid' => implode(',', $workunit_ids),
                'status' => 'Pending', // Set to pending for admin approval
                'createddate' => getCurrentDate(),
                'createdby' => 0 // Self registration
            );

            $this->superadmins->insert($user_data);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Database insert failed');
            }

            $this->db->trans_commit();

            // Log registration event
            logSecurityEvent('USER_REGISTRATION', [
                'username' => $username,
                'business_name' => $business_name,
                'village_id' => $village_id,
                'ip_address' => $this->input->ip_address()
            ], 'LOW');

            return JSONResponseDefault('OK', 'Registrasi berhasil. Akun Anda akan diaktivasi setelah diverifikasi oleh admin.');
        } catch (Exception $e) {
            $this->db->trans_rollback();

            // Log error
            logSecurityEvent('REGISTRATION_ERROR', [
                'username' => $username,
                'error' => $e->getMessage(),
                'ip_address' => $this->input->ip_address()
            ], 'HIGH');

            return JSONResponseDefault('FAILED', 'Terjadi kesalahan saat registrasi. Silakan coba lagi.');
        }
    }

    public function get_villages()
    {
        $district_code = getPost('district_code');

        if (empty($district_code)) {
            return JSONResponse(array());
        }

        $villages = $this->db->select('code, name')
            ->from('msvillages')
            ->where('district_code', $district_code)
            ->order_by('name', 'ASC')
            ->get()
            ->result();

        return JSONResponse($villages);
    }

    public function get_districts()
    {
        $city_code = getPost('city_code');

        if (empty($city_code)) {
            return JSONResponse(array());
        }

        $districts = $this->db->select('code, name')
            ->from('msdistricts')
            ->where('city_code', $city_code)
            ->order_by('name', 'ASC')
            ->get()
            ->result();

        return JSONResponse($districts);
    }

    public function get_provinces()
    {
        $provinces = $this->db->select('code, name')
            ->from('msprovinces')
            ->order_by('name', 'ASC')
            ->get()
            ->result();

        return JSONResponse($provinces);
    }

    public function get_cities()
    {
        $province_code = getPost('province_code');

        if (empty($province_code)) {
            return JSONResponse(array());
        }

        $cities = $this->db->select('code, name')
            ->from('mscities')
            ->where('province_code', $province_code)
            ->order_by('name', 'ASC')
            ->get()
            ->result();

        return JSONResponse($cities);
    }
}
