<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!DOCTYPE html>
<html lang="en" data-theme="light">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title : 'BUMDes System' ?></title>

    <link rel="icon" type="image/png" href="<?= base_url('wowdash') ?>/images/favicon.png" sizes="16x16">

    <!-- remix icon font css  -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/remixicon.css">

    <!-- BootStrap css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/lib/bootstrap.min.css">

    <!-- Select2 css -->
    <link rel="stylesheet" href="<?= base_url() ?>node_modules/select2/dist/css/select2.min.css">

    <!-- main css -->
    <link rel="stylesheet" href="<?= base_url('wowdash') ?>/css/style.css">



    <style>
        /* Use exact same styling as wowdash reference */
        .auth {
            min-height: 100vh;
        }

        .auth-left {
            background: linear-gradient(90deg, #ECF0FF 0%, #FAFBFF 100%);
            width: 50%;
        }

        .auth-right {
            width: 50%;
        }

        @media (max-width: 991px) {
            .auth-right {
                width: 100%;
            }
        }

        @media (max-width: 768px) {
            .auth-right>div {
                max-width: 100%;
                width: 100%;
            }
        }

        .toggle-password {
            cursor: pointer;
        }

        .center-border-horizontal {
            position: relative;
        }

        .center-border-horizontal::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e5e7eb;
        }

        .center-border-horizontal span {
            background: #fff;
            padding: 0 1rem;
            position: relative;
            z-index: 1;
        }
    </style>
</head>

<body>
    <?php $this->load->view($content); ?>

    <!-- jQuery library js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/jquery-3.7.1.min.js"></script>

    <!-- Bootstrap js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/bootstrap.bundle.min.js"></script>

    <!-- Select2 js -->
    <script src="<?= base_url() ?>node_modules/select2/dist/js/select2.full.min.js"></script>

    <!-- SweetAlert JS -->
    <script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>

    <!-- jQuery Validate JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>

    <!-- Custom Script JS -->
    <script src="<?= base_url('assets/js/ajax-request.js') ?>"></script>
    <script src="<?= base_url('assets/js/script.js') ?>"></script>



    <!-- Iconify Icon js -->
    <script src="<?= base_url('wowdash') ?>/js/lib/iconify-icon.min.js"></script>



    <!-- main js -->
    <script src="<?= base_url('wowdash') ?>/js/app.js"></script>

    <!-- Custom JS -->
    <script src="<?= base_url('wowdash') ?>/js/custom.js"></script>

    <script>
        // SweetAlert functions
        function swalMessageSuccess(message, callback) {
            Swal.fire({
                title: 'Berhasil!',
                text: message,
                icon: 'success',
                confirmButtonText: 'OK'
            }).then(callback);
        }

        function swalMessageFailed(message) {
            Swal.fire({
                title: 'Gagal!',
                text: message,
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }

        function swalError() {
            Swal.fire({
                title: 'Error!',
                text: 'Terjadi kesalahan sistem. Silakan coba lagi.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }

        // Ajax Request function
        $.AjaxRequest = function(form, options) {
            $(form).on('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const url = $(this).attr('action');

                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    dataType: 'json',
                    beforeSend: function() {
                        $(form + ' button[type="submit"]').prop('disabled', true).text('Memproses...');
                    },
                    success: function(response) {
                        if (options.success) {
                            options.success(response);
                        }
                    },
                    error: function() {
                        if (options.error) {
                            options.error();
                        }
                    },
                    complete: function() {
                        $(form + ' button[type="submit"]').prop('disabled', false).text('Daftar Sekarang');
                    }
                });
            });
        };

        // Toggle password visibility
        $(document).on('click', '.toggle-password', function() {
            const target = $(this).data('toggle');
            const input = $(target);
            const icon = $(this);

            if (input.attr('type') === 'password') {
                input.attr('type', 'text');
                icon.removeClass('ri-eye-line').addClass('ri-eye-off-line');
            } else {
                input.attr('type', 'password');
                icon.removeClass('ri-eye-off-line').addClass('ri-eye-line');
            }
        });
    </script>
</body>

</html>