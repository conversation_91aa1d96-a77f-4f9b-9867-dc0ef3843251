<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<section class="auth bg-base d-flex flex-wrap">
    <div class="auth-left d-lg-block d-none">
        <div class="d-flex align-items-center flex-column h-100 justify-content-center">
            <img src="<?= base_url('wowdash') ?>/images/auth/auth-img.png" alt="">
        </div>
    </div>
    <div class="auth-right py-32 px-24 d-flex flex-column justify-content-center">
        <div class="max-w-464-px mx-auto w-100">
            <div>
                <a href="<?= base_url() ?>" class="mb-40 max-w-290-px">
                    <img src="<?= base_url('wowdash') ?>/images/logo.png" alt="">
                </a>
                <h4 class="mb-12">Registrasi BUMDes</h4>
                <p class="mb-32 text-secondary-light text-lg">Daftarkan BUMDes Anda untuk mengakses sistem</p>
            </div>

            <form id="frmRegister" action="<?= base_url('register/process') ?>" method="POST" autocomplete="off">
                <div class="icon-field mb-16">
                    <span class="icon top-50 translate-middle-y">
                        <iconify-icon icon="f7:person"></iconify-icon>
                    </span>
                    <input type="text" class="form-control h-56-px bg-neutral-50 radius-12" id="business_owner" name="business_owner" placeholder="Nama Pemilik Usaha" required>
                </div>

                <div class="icon-field mb-16">
                    <span class="icon top-50 translate-middle-y">
                        <iconify-icon icon="solar:buildings-2-outline"></iconify-icon>
                    </span>
                    <input type="text" class="form-control h-56-px bg-neutral-50 radius-12" id="business_name" name="business_name" placeholder="Nama Usaha" required>
                </div>

                <div class="icon-field mb-16">
                    <span class="icon top-50 translate-middle-y">
                        <iconify-icon icon="solar:user-outline"></iconify-icon>
                    </span>
                    <input type="text" class="form-control h-56-px bg-neutral-50 radius-12" id="username" name="username" placeholder="Username" required>
                </div>
                <span class="mt-12 text-sm text-secondary-light mb-16 d-block">Username minimal 4 karakter</span>

                <div class="mb-20">
                    <div class="position-relative">
                        <div class="icon-field">
                            <span class="icon top-50 translate-middle-y">
                                <iconify-icon icon="solar:lock-password-outline"></iconify-icon>
                            </span>
                            <input type="password" class="form-control h-56-px bg-neutral-50 radius-12" id="password" name="password" placeholder="Password" required>
                        </div>
                        <span class="toggle-password ri-eye-line cursor-pointer position-absolute end-0 top-50 translate-middle-y me-16 text-secondary-light" data-toggle="#password"></span>
                    </div>
                    <span class="mt-12 text-sm text-secondary-light">Password minimal 6 karakter</span>
                </div>

                <div class="mb-20">
                    <div class="position-relative">
                        <div class="icon-field">
                            <span class="icon top-50 translate-middle-y">
                                <iconify-icon icon="solar:lock-password-outline"></iconify-icon>
                            </span>
                            <input type="password" class="form-control h-56-px bg-neutral-50 radius-12" id="confirm_password" name="confirm_password" placeholder="Konfirmasi Password" required>
                        </div>
                        <span class="toggle-password ri-eye-line cursor-pointer position-absolute end-0 top-50 translate-middle-y me-16 text-secondary-light" data-toggle="#confirm_password"></span>
                    </div>
                </div>

                <div class="icon-field mb-16">
                    <span class="icon top-50 translate-middle-y">
                        <iconify-icon icon="solar:map-outline"></iconify-icon>
                    </span>
                    <select class="form-control h-56-px bg-neutral-50 radius-12" id="province_id" name="province_id" required>
                        <option value="">Pilih Provinsi</option>
                        <?php foreach ($provinces as $province): ?>
                            <option value="<?= $province->code ?>"><?= $province->name ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="icon-field mb-16">
                    <span class="icon top-50 translate-middle-y">
                        <iconify-icon icon="solar:city-outline"></iconify-icon>
                    </span>
                    <select class="form-control h-56-px bg-neutral-50 radius-12" id="city_id" name="city_id" required>
                        <option value="">Pilih Kabupaten/Kota</option>
                    </select>
                </div>

                <div class="icon-field mb-16">
                    <span class="icon top-50 translate-middle-y">
                        <iconify-icon icon="solar:map-point-outline"></iconify-icon>
                    </span>
                    <select class="form-control h-56-px bg-neutral-50 radius-12" id="district_id" name="district_id" required>
                        <option value="">Pilih Kecamatan</option>
                    </select>
                </div>

                <div class="icon-field mb-16">
                    <span class="icon top-50 translate-middle-y">
                        <iconify-icon icon="solar:home-outline"></iconify-icon>
                    </span>
                    <select class="form-control h-56-px bg-neutral-50 radius-12" id="village_id" name="village_id" required>
                        <option value="">Pilih Desa</option>
                    </select>
                </div>

                <div class="mb-16">
                    <label class="form-label mb-8">Unit Usaha</label>
                    <select class="form-control" id="workunit_ids" name="workunit_ids[]" multiple required>
                        <?php foreach ($workunits as $workunit): ?>
                            <option value="<?= $workunit->id ?>"><?= $workunit->workunitname ?></option>
                        <?php endforeach; ?>
                    </select>
                    <span class="mt-8 text-sm text-secondary-light d-block">Pilih unit usaha yang akan dikelola (dapat memilih lebih dari satu)</span>
                </div>

                <div class="mb-20">
                    <div class="form-check style-check d-flex align-items-start">
                        <input class="form-check-input border border-neutral-300 mt-4" type="checkbox" id="agree_terms" required>
                        <label class="form-check-label text-sm" for="agree_terms">
                            Saya setuju dengan syarat dan ketentuan yang berlaku
                        </label>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary text-sm btn-sm px-12 py-16 w-100 radius-12 mt-32">
                    Daftar Sekarang
                </button>
            </form>

            <div class="mt-32 center-border-horizontal text-center">
                <span class="bg-base z-1 px-4">Sudah punya akun?</span>
            </div>
            <a href="<?= base_url('auth/login') ?>" class="btn btn-outline-primary text-sm btn-sm px-12 py-16 w-100 radius-12 mt-32">Login di sini</a>
        </div>
    </div>
</section>

<style>
    #workunit_ids+.select2-container {
        width: 100% !important;
    }

    #workunit_ids+.select2-container .select2-selection {
        width: 100% !important;
    }

    #workunit_ids+.select2-container .select2-selection--multiple {
        background-color: #f8f9fa !important;
        border: 1px solid #e0e6ed !important;
        border-radius: 12px !important;
        min-height: 56px !important;
        padding: 8px 12px !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice {
        background-color: #487fff !important;
        border: 1px solid #487fff !important;
        border-radius: 6px !important;
        color: white !important;
        padding: 4px 8px !important;
        margin: 2px !important;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
        color: white !important;
        margin-right: 5px !important;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
        color: #ff6b6b !important;
    }

    .select2-dropdown {
        border-radius: 8px !important;
        border: 1px solid #e0e6ed !important;
    }
</style>

<script>
    window.onload = function() {
        // Initialize Select2 for work units
        $('#workunit_ids').css('width', '100%').select2({
            placeholder: 'Pilih unit usaha',
            allowClear: true,
            width: '100%',
            theme: 'default'
        });

        // Ensure full width after initialization
        setTimeout(function() {
            $('#workunit_ids').next('.select2-container').css({
                'width': '100%',
                'display': 'block'
            });
            $('#workunit_ids').next('.select2-container').find('.select2-selection').css({
                'width': '100%'
            });
        }, 50);

        // Province change event
        $('#province_id').change(function() {
            const provinceCode = $(this).val();
            $('#city_id').html('<option value="">Pilih Kabupaten/Kota</option>');
            $('#district_id').html('<option value="">Pilih Kecamatan</option>');
            $('#village_id').html('<option value="">Pilih Desa</option>');

            if (provinceCode) {
                loadCities(provinceCode);
            }
        });

        // City change event
        $('#city_id').change(function() {
            const cityCode = $(this).val();
            $('#district_id').html('<option value="">Pilih Kecamatan</option>');
            $('#village_id').html('<option value="">Pilih Desa</option>');

            if (cityCode) {
                loadDistricts(cityCode);
            }
        });

        // District change event
        $('#district_id').change(function() {
            const districtCode = $(this).val();
            $('#village_id').html('<option value="">Pilih Desa</option>');

            if (districtCode) {
                loadVillages(districtCode);
            }
        });

        // Form submission
        $.AjaxRequest('#frmRegister', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return swalMessageSuccess(response.MESSAGE, ok => {
                        return window.location.href = '<?= base_url('auth/login') ?>';
                    })
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });
    };

    function loadCities(provinceCode) {
        $.post('<?= base_url('register/get_cities') ?>', {
            province_code: provinceCode
        }, function(response) {
            let options = '<option value="">Pilih Kabupaten/Kota</option>';
            response.forEach(function(city) {
                options += `<option value="${city.code}">${city.name}</option>`;
            });
            $('#city_id').html(options);
        });
    }

    function loadDistricts(cityCode) {
        $.post('<?= base_url('register/get_districts') ?>', {
            city_code: cityCode
        }, function(response) {
            let options = '<option value="">Pilih Kecamatan</option>';
            response.forEach(function(district) {
                options += `<option value="${district.code}">${district.name}</option>`;
            });
            $('#district_id').html(options);
        });
    }

    function loadVillages(districtCode) {
        $.post('<?= base_url('register/get_villages') ?>', {
            district_code: districtCode
        }, function(response) {
            let options = '<option value="">Pilih Desa</option>';
            response.forEach(function(village) {
                options += `<option value="${village.code}">${village.name}</option>`;
            });
            $('#village_id').html(options);
        });
    }
</script>